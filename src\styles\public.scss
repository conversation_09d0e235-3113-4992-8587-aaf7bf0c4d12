// 文本省略
.text-ellipsis-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.text-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
}

:root {
  --primary-bg-color: #EAEBFF;
  --primary-text-gradient: linear-gradient(180.00000000000006deg, #8163EE 0%, #1872FF 100%);
  ;
  --primary-btnbg-color: linear-gradient(270deg, #B892CE 0%, #8677EC 100%);
  --plyr-range-fill-background: #387FF5;
}

.primary-text-color {
  background: var(--primary-text-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  cursor: pointer;
}

/* 分页组件全局样式 */
.el-pagination {

  .el-pagination__total,
  .el-pagination__jump {
    font-weight: 400;
    font-size: 14px;
    color: #2A2D33;

  }

  .el-pager li {
    background-color: #FFFFFF !important;
    font-size: 14px;
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    margin: 0 4px;
    border: 1px solid #D8E2EB;
    font-family: Source Han Sans CN, Source Han Sans CN;

    &:hover {
      color: #1872FF;
    }

    &.is-active {
      background: linear-gradient(270deg, #8163EE 0%, #1872FF 100%);
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
    }
  }

  .btn-prev,
  .btn-next {
    background-color: #FFFFFF !important;
    border-radius: 4px;
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #D8E2EB;
    font-family: Source Han Sans CN, Source Han Sans CN;

    &:hover {
      color: #1872FF;
    }
  }
}

.main {
  width: 1160px;
  margin: 0 auto;
  height: 100%;
}

.el-message {
  top: 20% !important;
  margin-top: -100px !important;
  z-index: 100000002 !important;
}

.w1440 {
  width: 1440px;
  margin: 0 auto;
}

.w1280 {
  width: 1280px;
  margin: 0 auto;
}

#app {
  min-width: 1200px;
}

li {
  list-style: none;
}

.defbtn {
  background: #386CFC !important;
  height: 36px !important;
  padding: 9px 12px !important;
  border-radius: 4px !important;
  font-family: Source Han Sans CN, Source Han Sans CN !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #FFFFFF !important;
  font-style: normal;
  text-transform: none;

}

// dropdown
.el-dropdown-menu {
  min-width: 88px;
  border-radius: 8px;
  text-align: center;
  overflow: hidden;
  padding: 0 !important;
}

/* 消除小三角 */
.el-popper[x-placement^=bottom] .popper__arrow {
  border: none !important;
}

.el-popper[x-placement^=bottom] .popper__arrow::after {
  border: none !important;
}

.el-dropdown-menu__item {
  height: 40px !important;
  line-height: 40px !important;
  text-align: center;
  margin: 0 auto;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.commonSelect {
  height: 40px;

  .el-select__wrapper {
    height: 40px;
    line-height: 40px;
  }
}

.el-dropdown-menu__item:hover,
.el-dropdown-item:focus {
  background-color: #4285F4 !important;
  color: white !important;
}

// el-select
.el-select-dropdown__list {
  padding: 0px 0px !important;

  .el-select-dropdown__item {
    height: 42px;
    line-height: 42px;
  }

  .is-selected,
  .is-hovering {
    background-color: #4285F4 !important;
    color: white !important;
  }
}

// 下拉框距离选择框的间隙
.el-popper[x-placement^=bottom] {
  // margin-top: 8px;
}

.content-container {
  background: linear-gradient(90deg, #F5F8FC 0%, #F7F5FC 100%);
}